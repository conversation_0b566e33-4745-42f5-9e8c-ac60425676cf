#include "./pulse_type_detector.h"
#include "../../../video_processor_configs.h"

namespace IQVideoProcessor {

PulseTypeDetector::PulseTypeDetector(const SampleRateType sampleRate): sampleRate_(sampleRate) {
  HSyncRange = { // 5 - 7 microseconds
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_SYNC_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_SYNC_PULSE_MAX_WIDTH_SEC)
  };
  EqualizingRange = { // 1.5 - 3.5 microseconds
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(EQUALIZING_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(EQUALIZING_PULSE_MAX_WIDTH_SEC)
  };
  VSyncRange = { // 26 - 30 microseconds
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MAX_WIDTH_SEC)
  };
  VSyncLongRange = { // 440 - 500 microseconds
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MAX_WIDTH_SEC)
  };
}

[[nodiscard]] VideoSyncPulseType PulseTypeDetector::detectPulseType(const TFloat pulseWidth) const {
  if (HSyncRange.inRange(pulseWidth)) {
    return VideoSyncPulseType::HORIZONTAL_SYNC_PULSE;
  }
  if (EqualizingRange.inRange(pulseWidth)) {
    return VideoSyncPulseType::EQUALIZING_PULSE;
  }
  if (VSyncRange.inRange(pulseWidth)) {
    return VideoSyncPulseType::VERTICAL_SYNC_PULSE;
  }
  if (VSyncLongRange.inRange(pulseWidth)) {
    return VideoSyncPulseType::VERTICAL_SYNC_LONG_PULSE;
  }
  return VideoSyncPulseType::UNKNOWN;
}

} // namespace IQVideoProcessor