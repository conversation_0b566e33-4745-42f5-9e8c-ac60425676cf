#pragma once
#include "./video-sync-detector.h"
#include "../../../types.h"

namespace IQVideoProcessor {

struct VideoSyncPulse: VideoSyncDetector::Result {
  VideoSyncPulseType type{};
  static VideoSyncPulse fromSyncResult(
    const VideoSyncDetector::Result &r,
    const VideoSyncPulseType t,
    const TFloat absolutePosition
  ) {
    VideoSyncPulse vsp;
    vsp.fallingFrontPosition = r.fallingFrontPosition + absolutePosition;
    vsp.risingFrontPosition = r.risingFrontPosition + absolutePosition;
    vsp.centerPosition = r.centerPosition + absolutePosition;
    vsp.width = r.width;
    vsp.type = t;
    return vsp;
  };
};

}
