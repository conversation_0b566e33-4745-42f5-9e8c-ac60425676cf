#pragma once
#include "../../../types.h"
#include "../../../../types.h"
#include "../../../utils/range/range.h"

namespace IQVideoProcessor {

class PulseTypeDetector {
public:
  explicit PulseTypeDetector(SampleRateType sampleRate);
  ~PulseTypeDetector() = default;

  [[nodiscard]] VideoSyncPulseType detectPulseType(TFloat pulseWidth) const;

protected:

  Range<TFloat> HSyncRange{};
  Range<TFloat> EqualizingRange{};
  Range<TFloat> VSyncRange{};
  Range<TFloat> VSyncLongRange{};

  SampleRateType sampleRate_;
};

}