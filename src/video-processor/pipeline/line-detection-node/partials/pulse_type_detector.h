#pragma once
#include "../../../types.h"
#include "../../../../types.h"

namespace IQVideoProcessor {

class PulseTypeDetector {
public:
  explicit PulseTypeDetector(SampleRateType sampleRate);
  ~PulseTypeDetector() = default;

  [[nodiscard]] VideoSyncPulseType detectPulseType(TFloat pulseWidth) const;

protected:
  struct Range {
    TFloat from;
    TFloat to;
    [[nodiscard]] bool inRange(TFloat val) const;
  };

  Range HSyncRange{};
  Range EqualizingRange{};
  Range VSyncRange{};
  Range VSyncLongRange{};

  SampleRateType sampleRate_;
};

}