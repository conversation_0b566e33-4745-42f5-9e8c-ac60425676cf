#pragma once
#include "../../iq_demodulation_node_types.h"

namespace IQVideoProcessor::Pipeline {

struct AveFilteredDemodulatedSegment: DemodulatedSegment {
  size_t aveSize = 0;
  size_t halfAveSize = 0;
};

class SegmentAveFilter {
public:
  explicit SegmentAveFilter(SampleRateType sampleRate);
  virtual ~SegmentAveFilter() = default;
  const AveFilteredDemodulatedSegment& process(const DemodulatedSegment &segment);

private:
  SampleRateType sampleRate_;
  AveFilteredDemodulatedSegment _ds500kHz_;
};

}
