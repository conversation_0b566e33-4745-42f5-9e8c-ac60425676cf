#include "./video_standard_detector.h"
#include "../../../video_processor_configs.h"


namespace IQVideoProcessor {

constexpr size_t SYNC_PULSES_PROCESS_THRESHOLD = 625 * 3; // Process after collecting 5 frames of 625 lines
constexpr size_t SYNC_PULSES_BUFFER_SIZE = SYNC_PULSES_PROCESS_THRESHOLD * 2; // Buffer up to 10 frames of 625 lines
constexpr size_t GIVE_UP_ITERATIONS_THRESHOLD = static_cast<size_t>(SYNC_PULSES_PROCESS_THRESHOLD / LINES_PER_CHUNK) * 3;

VideoStandardDetector::VideoStandardDetector(const SampleRateType sampleRate) : sampleRate_(sampleRate) {
  collectedSyncPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  discoveredHorizontalPulses_.resize(SYNC_PULSES_BUFFER_SIZE);
  collectedSyncPulses_.clear();
  discoveredHorizontalPulses_.clear();

  horizontalOrEqualizingPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(EQUALIZING_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(H_SYNC_PULSE_MAX_WIDTH_SEC)
  };
  verticalSyncPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_PULSE_MAX_WIDTH_SEC)
  };
  verticalSyncLongPulseWidthRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MIN_WIDTH_SEC),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(V_SYNC_LONG_PULSE_MAX_WIDTH_SEC)
  };
  horizontalLineDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(63.3e-6),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(64.2e-6)
  };
  horizontalLineHalfDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(31.7e-6),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(31.9e-6)
  };
  horizontalLine70PercentDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(47.0e-6),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(49.0e-6)
  };
  horizontalLine33PercentDistanceRange_ = {
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(18.0e-6),
    static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(22.0e-6)
  };
}

void VideoStandardDetector::reset() {
  giveUpIterations_ = 0;
  collectedSyncPulses_.clear();
  discoveredHorizontalPulses_.clear();
  result_ = Result{};
}

[[nodiscard]] VideoStandardDetector::Result VideoStandardDetector::processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses) {
  if (++giveUpIterations_ > GIVE_UP_ITERATIONS_THRESHOLD) {
    result_.status = DETECTION_FAILED;
    return result_;
  }

  collectedSyncPulses_.insert(collectedSyncPulses_.end(), syncPulses.begin(), syncPulses.end());
  if (collectedSyncPulses_.size() < SYNC_PULSES_PROCESS_THRESHOLD) {
    return result_; // Not enough pulses collected yet
  }

  // We have enough pulses to attempt detection
  // Analyze collected pulses to determine video standard

  for (auto i = 1u; i < collectedSyncPulses_.size() - 1; ++i) {
    const auto& prevPulse = collectedSyncPulses_[i - 1];
    const auto& currentPulse = collectedSyncPulses_[i];
    const auto& nextPulse = collectedSyncPulses_[i + 1];


  }

  return result_;
}

VideoSyncPulseType VideoStandardDetector::detectCurrentPulseType(
  const VideoSyncPulse &prevPulse,
  const VideoSyncPulse &currentPulse,
  const VideoSyncPulse &nextPulse
) const {
  auto const currentEstPulseType = estimatePulseType(currentPulse.width);
  auto const nextEstPulseType = estimatePulseType(nextPulse.width);
  auto const prevEstPulseType = estimatePulseType(prevPulse.width);

  auto const currentEstDistanceType = estimateLineDistanceType(currentPulse.centerPosition - prevPulse.centerPosition);
  auto const nextEstDistanceType = estimateLineDistanceType(nextPulse.centerPosition - currentPulse.centerPosition);

  if (currentEstDistanceType == HORIZONTAL_DISTANCE && nextEstDistanceType == HORIZONTAL_DISTANCE) {
    // We are dialing with horizontal pulse between two other horizontal pulses, consider it horizontal
    return VideoSyncPulseType::HORIZONTAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the last pulse in the lines series, it will be equalizing pulse
    return VideoSyncPulseType::EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the equalizing pulse between 2 other equalizing pulses, consider it equalizing
    return VideoSyncPulseType::EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == _70PERCENT_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the last equalizing pulse before vertical sync pulses, consider it equalizing
    return VideoSyncPulseType::EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == _70PERCENT_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the first vertical sync pulse after equalizing pulses, consider it vertical
    return VideoSyncPulseType::VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the middle vertical sync pulse between other vertical sync pulses, consider it vertical
    return VideoSyncPulseType::VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == _33PERCENT_HORIZONTAL_DISTANCE && currentEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the last vertical sync pulse before equalizing pulses, consider it vertical
    return VideoSyncPulseType::VERTICAL_SYNC_PULSE;
  }
  if (currentEstDistanceType == _33PERCENT_HORIZONTAL_DISTANCE && nextEstDistanceType == HALF_HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE && prevEstPulseType == VERTICAL_PULSE) {
    // We are dealing with the first equalizing pulse after vertical sync pulses, consider it equalizing
    return VideoSyncPulseType::EQUALIZING_PULSE;
  }
  if (currentEstDistanceType == HALF_HORIZONTAL_DISTANCE && nextEstDistanceType == HORIZONTAL_DISTANCE && currentEstPulseType == HORIZONTAL_OR_EQUALIZING_PULSE) {
    // We are dealing with the first horizontal pulse after vertical sync pulses and equalizing pulses, consider it horizontal
    return VideoSyncPulseType::HORIZONTAL_SYNC_PULSE;
  }

  return VideoSyncPulseType::UNKNOWN;
}


inline VideoStandardDetector::EstimatedPulseType VideoStandardDetector::estimatePulseType(const TFloat pulseWidth) const {
  if (horizontalOrEqualizingPulseWidthRange_.inRange(pulseWidth)) {
    return EstimatedPulseType::HORIZONTAL_OR_EQUALIZING_PULSE; // or EQUALIZING_PULSE, need more context to differentiate
  }
  if (verticalSyncPulseWidthRange_.inRange(pulseWidth)) {
    return EstimatedPulseType::VERTICAL_PULSE;
  }
  if (verticalSyncLongPulseWidthRange_.inRange(pulseWidth)) {
    return EstimatedPulseType::VERTICAL_LONG_PULSE;
  }
  return EstimatedPulseType::UNKNOWN_PULSE;
}

inline VideoStandardDetector::EstimatedLineDistanceType VideoStandardDetector::estimateLineDistanceType(TFloat distance) const {
  if (horizontalLineDistanceRange_.inRange(distance)) {
    return EstimatedLineDistanceType::HORIZONTAL_DISTANCE;
  }
  if (horizontalLineHalfDistanceRange_.inRange(distance)) {
    return EstimatedLineDistanceType::HALF_HORIZONTAL_DISTANCE;
  }
  if (horizontalLine70PercentDistanceRange_.inRange(distance)) {
    return EstimatedLineDistanceType::_70PERCENT_HORIZONTAL_DISTANCE;
  }
  if (horizontalLine33PercentDistanceRange_.inRange(distance)) {
    return EstimatedLineDistanceType::_33PERCENT_HORIZONTAL_DISTANCE;
  }
  return EstimatedLineDistanceType::UNKNOWN_DISTANCE;
}

} // namespace IQVideoProcessor