#pragma once

#include "../../../types.h"
#include "../../../../types.h"
#include "../../../utils/range/range.h"
#include "./video_sync_pulse.h"

#include <vector>

namespace IQVideoProcessor {


class VideoStandardDetector {
public:
  enum DetectionStatus {
    DETECTION_IN_PROGRESS = 0,
    DETECTION_COMPLETE = 10,
    DETECTION_FAILED = 20,
  };

  struct Result {
    VideoStandard standard{STANDARD_UNKNOWN};
    DetectionStatus status{DETECTION_IN_PROGRESS};
    TFloat horizontalFrequencyHz{0};      // ≈ 15625 or ≈ 15734
    TFloat fieldRateHz{0};                // ≈ 50 or ≈ 59.94
    TFloat frameRate{0};                  // FPS = fieldRateHz / 2 (if available)
    TFloat horizontalLineDuration{0};     // samples per line (normalized)
    uint32_t linesPerFrame{0};            // 625 or 525
  };

  explicit VideoStandardDetector(SampleRateType sampleRate);

  [[nodiscard]] Result processSegmentSyncPulses(const std::vector<VideoSyncPulse>& syncPulses);

  void reset();

private:
  enum EstimatedPulseType {
    UNKNOWN_PULSE = 0,
    HORIZONTAL_OR_EQUALIZING_PULSE = 10,
    VERTICAL_PULSE = 20,
    VERTICAL_LONG_PULSE = 30,
  };
  enum EstimatedLineDistanceType {
    UNKNOWN_DISTANCE = 0,
    HORIZONTAL_DISTANCE = 10,
    HALF_HORIZONTAL_DISTANCE = 20,
    _33PERCENT_HORIZONTAL_DISTANCE = 30,
    _70PERCENT_HORIZONTAL_DISTANCE = 40,
  };

  SampleRateType sampleRate_;
  Result result_;
  size_t giveUpIterations_{0};
  std::vector<VideoSyncPulse> collectedSyncPulses_;
  std::vector<VideoSyncPulse> discoveredHorizontalPulses_;

  // Processing ranges
  Range<TFloat> horizontalOrEqualizingPulseWidthRange_{}; // 1.5-7 microseconds
  Range<TFloat> verticalSyncPulseWidthRange_{}; // 26-30 microseconds
  Range<TFloat> verticalSyncLongPulseWidthRange_{}; // 440-500 microseconds
  Range<TFloat> horizontalLineDistanceRange_{}; // ~63.3-64.2 microseconds
  Range<TFloat> horizontalLineHalfDistanceRange_{}; // ~31.7-31.9 microseconds
  Range<TFloat> horizontalLine70PercentDistanceRange_{}; // ~42.0-46.0 microseconds
  Range<TFloat> horizontalLine33PercentDistanceRange_{}; // ~18.0-22.0 microseconds

  [[nodiscard]]

  VideoSyncPulseType detectCurrentPulseType(const VideoSyncPulse &prevPulse, const VideoSyncPulse &currentPulse, const VideoSyncPulse &nextPulse) const;


  [[nodiscard]] inline EstimatedPulseType estimatePulseType(TFloat pulseWidth) const;
  [[nodiscard]] inline EstimatedLineDistanceType estimateLineDistanceType(TFloat distance) const;
};

} // namespace IQVideoProcessor