#include "./line_detection_node.h"
#include "../../helpers/helpers.h"
#include "../../video_processor_configs.h"


#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> frontGraphic;
std::vector<TFloat> syncGraphic;
std::vector<TFloat> syncPulseGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  // Initialize default sync detection parameters
  processingConfig_.pulseMaxWidth = static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(PULSE_MAX_WIDTH_US); // 500 microseconds
  processingConfig_.pulseMaxSearchDistance = static_cast<TFloat>(sampleRate_) / MIN_LINE_RATE_HZ; // ~66.7 microseconds
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  const auto spc = getSegmentProcessingConfig(filteredSegment);

  // <DEBUGGING>
  if (frontGraphic.size() != segment.totalSamples) { frontGraphic.resize(segment.totalSamples); syncGraphic.resize(segment.totalSamples); syncPulseGraphic.resize(segment.totalSamples); }
  std::fill(frontGraphic.begin(), frontGraphic.end(), 0);
  std::fill(syncGraphic.begin(), syncGraphic.end(), spc.pulseSyncValue);
  std::fill(syncPulseGraphic.begin(), syncPulseGraphic.end(), -10);
  // </DEBUGGING>

  const auto startPosition = filteredSegment.effectiveStartPosition;
  VideoSyncDetector videoSyncDetector(
    &filteredSegment.data[filteredSegment.effectiveOffset],
    // Getting as many as we can, minus half the filter size to ensure we operate within valid data
    filteredSegment.effectiveSamples + filteredSegment.effectiveOffset - filteredSegment.halfAveSize - 1,
    spc
  );

  TFloat fromPosition = 0;
  while (videoSyncDetector.findNext(fromPosition)) {
    const auto& result = videoSyncDetector.getResult();
    fromPosition = result.risingFrontPosition;
    const auto pulseType = pulseTypeDetector_.detectPulseType(result.width);

    // <DEBUGGING>
    // Mark the center position of the detected sync pulse in the front graphic
    const auto centerPosition = static_cast<size_t>(result.centerPosition);
    frontGraphic[centerPosition] = result.width * static_cast<TFloat>(1e6) / static_cast<TFloat>(sampleRate_); // Convert width to microseconds for better visibility
    syncPulseGraphic[centerPosition] = static_cast<TFloat>(pulseType);
    // </DEBUGGING>
  }

  // <DEBUGGING>
  DevTools::export_debug_data<TFloat>("LDN", "pulseTypes", segment.segmentIndex, syncPulseGraphic.data(), syncPulseGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, frontGraphic.data(), frontGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "sync", segment.segmentIndex, syncGraphic.data(), syncGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>
  return running();
}

LineDetectionNode::SegmentProcessingConfig& LineDetectionNode::getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment) {
  const auto [minSampleValue, maxSampleValue] = Helpers::getMinMax(&segment.data[segment.effectiveOffset], segment.effectiveSamples);

  auto& config = processingConfig_;
  config.signalMinVal = minSampleValue;
  config.signalMaxVal = maxSampleValue;
  config.signalScale = maxSampleValue - minSampleValue;
  // Configure sync detection parameters based on the signal range and segment properties
  config.pulseThresholdTrigValueDelta = config.signalScale * static_cast<TFloat>(0.10); // 10% of the signal range
  config.pulseThresholdLength = static_cast<TFloat>(segment.aveSize) / static_cast<TFloat>(2); // Half of the average size of a filter
  config.pulseSyncValue = minSampleValue + config.signalScale * static_cast<TFloat>(0.18); // 18% of the signal range
  return config;
}

} // namespace IQVideoProcessor::Pipeline
