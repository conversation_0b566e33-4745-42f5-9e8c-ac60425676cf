#include "./line_detection_node.h"
#include "../../helpers/helpers.h"
#include "../../video_processor_configs.h"


#include "./devtools/data_exporter.hpp"

namespace IQVideoProcessor::Pipeline {

std::vector<TFloat> syncPositionsGraphic;
std::vector<TFloat> syncGraphic;
std::vector<TFloat> syncPulseGraphic;

LineDetectionNode::LineDetectionNode(const SampleRateType sampleRate) : sampleRate_(sampleRate), segmentSyncDetectionFilter_(sampleRate) {
  // Initialize default sync detection parameters
  processingConfig_.pulseMaxWidth = static_cast<TFloat>(sampleRate_) * static_cast<TFloat>(PULSE_MAX_WIDTH_US); // 500 microseconds
  processingConfig_.pulseMaxSearchDistance = static_cast<TFloat>(sampleRate_) / MIN_LINE_RATE_HZ; // ~66.7 microseconds
  detectedVideoSyncPulses_.resize(1000); // Preallocate for efficiency
  setRunning();
}

LineDetectionNode::~LineDetectionNode() {
  PipelineComponent::stop();
}

bool LineDetectionNode::process(DemodulatedSegment& segment) {
  if (!running()) return false;

  auto& filteredSegment = segmentSyncDetectionFilter_.process(segment);
  detectSegmentVideoSyncPulses(filteredSegment);

  // <DEBUGGING>
  if (syncPositionsGraphic.size() != segment.totalSamples) {
    syncPositionsGraphic.resize(segment.totalSamples);
    syncGraphic.resize(segment.totalSamples);
    syncPulseGraphic.resize(segment.totalSamples);
  }
  std::fill(syncPositionsGraphic.begin(), syncPositionsGraphic.end(), 0);
  std::fill(syncPulseGraphic.begin(), syncPulseGraphic.end(), -10);
  for (const auto & detected_video_sync_pulse : detectedVideoSyncPulses_) {
    auto centerPos = static_cast<uint32_t>(detected_video_sync_pulse.centerPosition) + segment.effectiveOffset;
    auto fallingPos = static_cast<uint32_t>(detected_video_sync_pulse.fallingFrontPosition) + segment.effectiveOffset;
    auto risingPos = static_cast<uint32_t>(detected_video_sync_pulse.risingFrontPosition) + segment.effectiveOffset;
    syncPositionsGraphic[fallingPos] = -35;
    syncPositionsGraphic[risingPos] = -35;
    syncPulseGraphic[centerPos] = static_cast<TFloat>(detected_video_sync_pulse.type);
  }
  // </DEBUGGING>

  auto result = videoStandardDetector_.processSegmentSyncPulses(detectedVideoSyncPulses_);

  // <DEBUGGING>
  DevTools::export_debug_data<TFloat>("LDN", "pulseTypes", segment.segmentIndex, syncPulseGraphic.data(), syncPulseGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "fronts", segment.segmentIndex, syncPositionsGraphic.data(), syncPositionsGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "sync", segment.segmentIndex, syncGraphic.data(), syncGraphic.size());
  DevTools::export_debug_data<TFloat>("LDN", "original", segment.segmentIndex, segment.data.data(), segment.totalSamples);
  DevTools::export_debug_data<TFloat>("LDN", "ave500kHz", filteredSegment.segmentIndex, filteredSegment.data.data(), filteredSegment.totalSamples);
  // </DEBUGGING>
  return running();
}

LineDetectionNode::SegmentProcessingConfig& LineDetectionNode::getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment) {
  const auto [minSampleValue, maxSampleValue] = Helpers::getMinMax(&segment.data[segment.effectiveOffset], segment.effectiveSamples);

  auto& config = processingConfig_;
  config.signalMinVal = minSampleValue;
  config.signalMaxVal = maxSampleValue;
  config.signalScale = maxSampleValue - minSampleValue;
  // Configure sync detection parameters based on the signal range and segment properties
  config.pulseThresholdTrigValueDelta = config.signalScale * static_cast<TFloat>(0.10); // 10% of the signal range
  config.pulseThresholdLength = static_cast<TFloat>(segment.aveSize) / static_cast<TFloat>(2); // Half of the average size of a filter
  config.pulseSyncValue = minSampleValue + config.signalScale * static_cast<TFloat>(0.18); // 18% of the signal range
  return config;
}

void LineDetectionNode::detectSegmentVideoSyncPulses(const AveFilteredDemodulatedSegment &filteredSegment) {
  detectedVideoSyncPulses_.clear(); // Clear previous detections

  const auto spc = getSegmentProcessingConfig(filteredSegment);
  const auto absolutePosition = static_cast<TFloat>(filteredSegment.effectiveStartPosition);

  // <DEBUGGING>
  std::fill(syncGraphic.begin(), syncGraphic.end(), spc.pulseSyncValue);
  // </DEBUGGING>

  VideoSyncDetector videoSyncDetector(
    &filteredSegment.data[filteredSegment.effectiveOffset],
    // Getting as many as we can, minus half the filter size to ensure we operate within valid data
    filteredSegment.effectiveSamples + filteredSegment.effectiveOffset - filteredSegment.halfAveSize - 1,
    spc
  );

  TFloat fromPosition = nextSegmentFromPosition_; // Continue from last position
  const auto toPosition = static_cast<TFloat>(filteredSegment.effectiveSamples);
  while (videoSyncDetector.findNext(fromPosition)) {
    const auto& result = videoSyncDetector.getResult();
    const auto pulseType = pulseTypeDetector_.detectPulseType(result.width);
    auto vsp = VideoSyncPulse::fromSyncResult(result, pulseType, absolutePosition);
    detectedVideoSyncPulses_.push_back(vsp);

    fromPosition = result.risingFrontPosition; // Move to the end of the detected pulse for next search

    if (fromPosition >= toPosition) {
      nextSegmentFromPosition_ = fromPosition - toPosition; // Store overflow for next segment
      return;
    }
  }
  nextSegmentFromPosition_ = 0; // Reset if we haven't found anything within this segment
}


} // namespace IQVideoProcessor::Pipeline
