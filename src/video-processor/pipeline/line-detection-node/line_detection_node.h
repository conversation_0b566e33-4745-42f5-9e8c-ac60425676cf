#pragma once

#include "../../../stream-pipeline/stream_node.h"
#include "../../../types.h"
#include "../iq_demodulation_node_types.h"
#include "./partials/segment-ave-filter.h"
#include "./partials/video-sync-detector.h"
#include "./partials/pulse_type_detector.h"

namespace IQVideoProcessor::Pipeline {

class LineDetectionNode final : public SPipeline::StreamNode<DemodulatedSegment, DemodulatedSegment> {
public:
  explicit LineDetectionNode(SampleRateType sampleRate);
  ~LineDetectionNode() override;

private:
  bool process(DemodulatedSegment& segment) override;

  struct SegmentProcessingConfig: VideoSyncDetector::Config {
    TFloat signalMinVal;
    TFloat signalMaxVal;
    TFloat signalScale;
  };

  SampleRateType sampleRate_;
  SegmentAveFilter segmentSyncDetectionFilter_;
  SegmentProcessingConfig processingConfig_{};
  PulseTypeDetector pulseTypeDetector_{sampleRate_};

  SegmentProcessingConfig& getSegmentProcessingConfig(const AveFilteredDemodulatedSegment &segment);
};

} // namespace IQVideoProcessor::Pipeline
